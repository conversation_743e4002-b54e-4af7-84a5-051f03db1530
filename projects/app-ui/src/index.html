<!doctype html>
<html lang="zh-<PERSON>">
<head>
  <meta charset="utf-8">
  <title>AI 助理</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link href="/css/roboto.css" rel="stylesheet">
  <link href="/css/material-icons.css" rel="stylesheet">
</head>
<body>
<app-root>
  <style>
    /* 启动动画样式 */
    .app-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, "<PERSON><PERSON><PERSON> UI", <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, "<PERSON><PERSON> Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    }

    .app-loading-logo {
      width: 120px;
      height: 120px;
      margin-bottom: 20px;
      animation: pulse 1.5s ease-in-out infinite alternate;
    }

    .app-loading-title {
      font-size: 24px;
      font-weight: 500;
      color: #1976d2;
      margin-bottom: 16px;
      opacity: 0;
      animation: fadeIn 0.8s ease-in-out forwards 0.5s;
    }

    .app-loading-spinner {
      width: 50px;
      height: 50px;
    }

    .app-loading-spinner circle {
      fill: none;
      stroke: #1976d2;
      stroke-width: 4;
      stroke-dasharray: 150, 200;
      stroke-dashoffset: -10;
      stroke-linecap: round;
      animation: dash 1.5s ease-in-out infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(0.95);
        opacity: 0.7;
      }
      100% {
        transform: scale(1.05);
        opacity: 1;
      }
    }

    @keyframes fadeIn {
      0% {
        opacity: 0;
        transform: translateY(10px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes dash {
      0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
      }
      50% {
        stroke-dasharray: 90, 200;
        stroke-dashoffset: -35;
      }
      100% {
        stroke-dasharray: 90, 200;
        stroke-dashoffset: -125;
      }
    }
  </style>

  <div class="app-loading">
    <svg class="app-loading-logo" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <circle cx="50" cy="50" r="40" fill="#e3f2fd"/>
      <path d="M35,35 Q50,20 65,35 Q80,50 65,65 Q50,80 35,65 Q20,50 35,35" fill="#1976d2"/>
      <circle cx="40" cy="40" r="5" fill="white"/>
      <circle cx="60" cy="40" r="5" fill="white"/>
      <path d="M40,60 Q50,70 60,60" stroke="white" stroke-width="3" fill="none" stroke-linecap="round"/>
    </svg>
    <div class="app-loading-title">AI 智能助理</div>
    <svg class="app-loading-spinner" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
      <circle cx="25" cy="25" r="20"/>
    </svg>
  </div>
</app-root>
</body>
</html>
