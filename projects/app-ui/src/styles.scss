// Custom Theming for Angular Material
// For more information: https://material.angular.dev/guide/theming
@use '@angular/material' as mat;

html {
  @include mat.theme((
          color: (
                  theme-type: light,
                  primary: mat.$azure-palette,
                  tertiary: mat.$blue-palette,
          ),
          typography: Roboto,
          density: 0,
  ));
}

/* You can add global styles to this file, and also import other style files */

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}
