{"name": "ai-assistant", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/cdk": "20.0.1", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "20.0.1", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "highlight.js": "^11.11.1", "katex": "^0.16.22", "markdown-it": "^14.1.0", "markdown-it-multimd-table": "^4.2.3", "mermaid": "^11.6.0", "plantuml-encoder": "^1.4.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@types/jasmine": "~5.1.0", "@types/markdown-it": "^14.1.2", "@types/node": "18.16.9", "@types/plantuml-encoder": "^1.4.2", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}